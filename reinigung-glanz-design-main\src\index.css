
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Apple-inspired design system variables */
@layer base {
  :root {
    /* Apple-Inspired Design System - SUZ Reinigung */

    /* Primary Brand Colors - Dark Theme (Now Default) */
    --suz-blue-primary: #0A84FF;
    --suz-blue-secondary: #64D2FF;
    --suz-blue-tertiary: #30D158;

    /* Dark Neutral Foundation */
    --suz-gray-50: #1C1C1E;
    --suz-gray-100: #2C2C2E;
    --suz-gray-200: #3A3A3C;
    --suz-gray-300: #48484A;
    --suz-gray-400: #636366;
    --suz-gray-500: #8E8E93;
    --suz-gray-600: #AEAEB2;
    --suz-gray-700: #C7C7CC;
    --suz-gray-800: #D1D1D6;
    --suz-gray-900: #F2F2F7;

    /* Dark Semantic Colors */
    --suz-success: #30D158;
    --suz-warning: #FF9F0A;
    --suz-error: #FF453A;
    --suz-info: #0A84FF;

    /* Dark Surface Colors */
    --suz-surface-primary: #000000;
    --suz-surface-secondary: #1C1C1E;
    --suz-surface-tertiary: #2C2C2E;
    --suz-surface-glass: rgba(28, 28, 30, 0.8);

    /* Typography Scale */
    --text-display-xl: 4.5rem;    /* 72px - Hero headlines */
    --text-display-lg: 3.75rem;   /* 60px - Section headers */
    --text-display-md: 3rem;      /* 48px - Page titles */
    --text-display-sm: 2.25rem;   /* 36px - Card titles */

    --text-heading-xl: 1.875rem;  /* 30px - H1 */
    --text-heading-lg: 1.5rem;    /* 24px - H2 */
    --text-heading-md: 1.25rem;   /* 20px - H3 */
    --text-heading-sm: 1.125rem;  /* 18px - H4 */

    --text-body-xl: 1.125rem;     /* 18px - Large body */
    --text-body-lg: 1rem;         /* 16px - Default body */
    --text-body-md: 0.875rem;     /* 14px - Small body */
    --text-body-sm: 0.75rem;      /* 12px - Caption */

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Spacing Scale (8px base unit) */
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */
    --space-24: 6rem;     /* 96px */
    --space-32: 8rem;     /* 128px */

    /* Layout Spacing */
    --section-padding-sm: var(--space-16);
    --section-padding-md: var(--space-20);
    --section-padding-lg: var(--space-24);
    --section-padding-xl: var(--space-32);

    --component-padding-sm: var(--space-4);
    --component-padding-md: var(--space-6);
    --component-padding-lg: var(--space-8);

    /* Border Radius System */
    --radius-none: 0;
    --radius-sm: 0.25rem;    /* 4px */
    --radius-md: 0.5rem;     /* 8px */
    --radius-lg: 0.75rem;    /* 12px */
    --radius-xl: 1rem;       /* 16px */
    --radius-2xl: 1.5rem;    /* 24px */
    --radius-3xl: 2rem;      /* 32px */
    --radius-full: 9999px;

    /* Shadow System */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-blue: 0 10px 25px -5px rgba(0, 122, 255, 0.15);
    --shadow-green: 0 10px 25px -5px rgba(52, 199, 89, 0.15);

    /* Animation Timing */
    --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
    --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
    --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --duration-fast: 150ms;
    --duration-normal: 250ms;
    --duration-slow: 350ms;
    --duration-slower: 500ms;

    /* Legacy Shadcn/UI Variables (Dark Theme - for compatibility) */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --radius: 0.5rem;
  }


}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 400;
  }

  body.force-apple-design {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Apple-inspired component styles */
@layer components {

/* Premium Background Gradient - Dark Theme Default */
.bg-premium-gradient {
  background: linear-gradient(135deg,
    #000000 0%,
    #1a1a1a 25%,
    #0f172a 50%,
    #1e293b 75%,
    #0f1419 100%) !important;
  min-height: 100vh !important;
  position: relative;
}

.bg-premium-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(10, 132, 255, 0.08) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(100, 210, 255, 0.06) 0%, transparent 50%);
  pointer-events: none;
}

/* Force Apple Design System Priority */
body.force-apple-design,
.force-apple-design {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.force-apple-design .bg-premium-gradient {
  background: linear-gradient(135deg,
    #000000 0%,
    #1a1a1a 25%,
    #0f172a 50%,
    #1e293b 75%,
    #0f1419 100%) !important;
}

/* Enhanced Glass Morphism Effects */
.glass-morphism {
  background: rgba(28, 28, 30, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px 0 rgba(10, 132, 255, 0.15);
}

.glass-morphism-premium {
  background: rgba(28, 28, 30, 0.4) !important;
  -webkit-backdrop-filter: blur(30px) !important;
  backdrop-filter: blur(30px) !important;
  box-shadow:
    0 20px 40px -10px rgba(10, 132, 255, 0.15),
    0 10px 25px -5px rgba(100, 210, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Enhanced Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6, #06b6d4, #0ea5e9, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
}

.gradient-text-animated {
  background: linear-gradient(135deg, #3b82f6, #06b6d4, #0ea5e9, #8b5cf6, #3b82f6) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  background-size: 300% 300% !important;
  animation: gradient-shift 6s ease-in-out infinite !important;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Pulse Glow Effect */
.pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    text-shadow: 0 0 30px rgba(59, 130, 246, 0.5), 0 0 40px rgba(6, 182, 212, 0.3);
  }
}

/* Logo Glow Effect */
.logo-glow:hover {
  box-shadow: 
    0 0 30px rgba(59, 130, 246, 0.4),
    0 0 60px rgba(6, 182, 212, 0.2),
    0 20px 40px -10px rgba(59, 130, 246, 0.15);
}

/* Premium Button Effects */
.premium-button {
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 10px 30px -5px rgba(0, 0, 0, 0.25),
    0 0 20px rgba(255, 255, 255, 0.2) inset;
}

.premium-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.premium-button:hover::before {
  left: 100%;
}

.premium-button-3d {
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 15px 35px -5px rgba(0, 0, 0, 0.2),
    0 5px 15px -3px rgba(0, 0, 0, 0.1),
    0 0 20px rgba(255, 255, 255, 0.3) inset;
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-button-3d:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 20px 40px -5px rgba(0, 0, 0, 0.25),
    0 8px 20px -3px rgba(0, 0, 0, 0.15),
    0 0 30px rgba(255, 255, 255, 0.4) inset;
}

/* Enhanced Service Card Hover Effect */
.service-card-premium {
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-card-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.08), 
    rgba(6, 182, 212, 0.08),
    rgba(139, 92, 246, 0.05));
  opacity: 0;
  transition: opacity 0.4s ease;
  border-radius: inherit;
}

.service-card-premium:hover::before {
  opacity: 1;
}

.service-card-premium:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 25px 50px -10px rgba(59, 130, 246, 0.15),
    0 10px 30px -5px rgba(6, 182, 212, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Icon Badge Style */
.icon-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.2));
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.4);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.15),
    0 4px 12px rgba(6, 182, 212, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-badge:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 30px rgba(59, 130, 246, 0.2),
    0 6px 15px rgba(6, 182, 212, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* Enhanced Icon Badge Style for Better Visibility */
.icon-badge-enhanced {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 96px;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.15),
    rgba(6, 182, 212, 0.12),
    rgba(255, 255, 255, 0.8));
  border-radius: 50%;
  border: 2px solid rgba(59, 130, 246, 0.2);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  box-shadow:
    0 12px 35px rgba(59, 130, 246, 0.25),
    0 6px 18px rgba(6, 182, 212, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.9),
    inset 0 -2px 0 rgba(59, 130, 246, 0.1);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.icon-badge-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.08));
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.icon-badge-enhanced:hover::before {
  opacity: 1;
}

.icon-badge-enhanced:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 20px 45px rgba(59, 130, 246, 0.35),
    0 10px 25px rgba(6, 182, 212, 0.25),
    inset 0 2px 0 rgba(255, 255, 255, 1),
    inset 0 -2px 0 rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.4);
}

/* Team Card Effects */
.team-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.team-card:hover {
  transform: translateY(-5px);
  box-shadow:
    0 20px 40px -10px rgba(59, 130, 246, 0.12),
    0 8px 25px -5px rgba(6, 182, 212, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

/* Leadership Card Enhanced Styling */
.leadership-card {
  position: relative;
  overflow: hidden;
}

.leadership-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.05),
    rgba(6, 182, 212, 0.03));
  border-radius: inherit;
  pointer-events: none;
}

.leadership-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 25px 50px -10px rgba(59, 130, 246, 0.2),
    0 12px 30px -5px rgba(6, 182, 212, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.8),
    0 0 0 1px rgba(59, 130, 246, 0.1);
}

/* Enhanced Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(2deg);
  }
  66% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

.floating-element {
  animation: float 8s ease-in-out infinite;
}

/* Enhanced Fade In Animation */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Premium Micro-Interactions */
@keyframes subtle-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

@keyframes gentle-scale {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-shimmer {
  animation: shimmer 1.5s ease-in-out;
}

@keyframes soft-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.4), 0 0 40px rgba(6, 182, 212, 0.2);
  }
}

/* Enhanced Company Showcase Infinite Scroll Animation - 60fps optimized */
@keyframes scroll-right {
  0% {
    transform: translateX(0) translateZ(0);
  }
  100% {
    transform: translateX(-50%) translateZ(0);
  }
}

/* WebKit-specific keyframes for iOS Safari compatibility */
@-webkit-keyframes scroll-right {
  0% {
    -webkit-transform: translateX(0) translateZ(0);
    transform: translateX(0) translateZ(0);
  }
  100% {
    -webkit-transform: translateX(-50%) translateZ(0);
    transform: translateX(-50%) translateZ(0);
  }
}

.animate-scroll-right {
  animation: scroll-right 60s linear infinite;
  /* Force hardware acceleration for smooth 60fps */
  will-change: transform;
  /* CRITICAL: Use translate3d for better mobile compatibility */
  transform: translate3d(0, 0, 0);
  /* Ensure animation works on all devices */
  animation-fill-mode: both;
  animation-play-state: running;
  /* Mobile-specific optimizations */
  -webkit-animation: scroll-right 60s linear infinite;
  -webkit-animation-play-state: running;
  -webkit-transform: translate3d(0, 0, 0);
  /* Prevent layout shifts during animation */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Enhanced Company Showcase Styles - Premium Dark Theme */
.suz-company-showcase {
  position: relative;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.95) 0%,
    rgba(28, 28, 30, 0.9) 25%,
    rgba(44, 44, 46, 0.95) 50%,
    rgba(28, 28, 30, 0.9) 75%,
    rgba(0, 0, 0, 0.95) 100%
  ) !important;
  -webkit-backdrop-filter: blur(30px) !important;
  backdrop-filter: blur(30px) !important;
  padding: var(--space-20) 0 !important;
  margin-top: var(--space-16) !important;
  margin-bottom: var(--space-16) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.suz-company-scroll {
  /* Optimize for smooth 60fps animation */
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
  width: max-content;
  /* GPU acceleration for better performance */
  transform: translateZ(0);
  /* Smooth animation timing */
  animation-timing-function: linear;
  /* Ensure proper display on all devices */
  display: flex;
  flex-wrap: nowrap;
  /* Prevent layout shifts */
  min-height: fit-content;
}

.suz-company-card {
  /* Prevent layout shifts during animation */
  contain: layout style paint;
  transform: translateZ(0);
  min-width: 300px;
  max-width: 350px;
}

/* Enhanced company card content styling */
.suz-company-card-content {
  padding: var(--space-6) !important;
}

/* Company showcase icon styling */
.suz-company-showcase .icon-badge-enhanced {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.2), rgba(100, 210, 255, 0.1));
  border: 1px solid rgba(10, 132, 255, 0.3);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.suz-company-showcase .icon-badge-enhanced:hover {
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.3), rgba(100, 210, 255, 0.2));
  border: 1px solid rgba(10, 132, 255, 0.5);
  box-shadow: 0 10px 20px rgba(10, 132, 255, 0.2);
}

.suz-company-card-content {
  padding: var(--component-padding-md);
}

/* Pause animation on hover for better UX - desktop only */
@media (min-width: 769px) {
  .suz-company-showcase:hover .animate-scroll-right {
    animation-play-state: paused;
  }
}

/* Ensure animation continues on mobile/touch devices */
@media (max-width: 768px) {
  .suz-company-showcase .animate-scroll-right {
    animation-play-state: running !important;
    -webkit-animation-play-state: running !important;
    /* Force hardware acceleration for mobile browsers */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    /* Optimize for mobile performance */
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
}

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .animate-scroll-right {
    animation: none !important;
    animation-play-state: paused !important;
  }

  .suz-company-scroll {
    overflow-x: auto;
    scroll-behavior: smooth;
    /* Provide horizontal scrolling fallback */
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
  }

  .suz-company-scroll::-webkit-scrollbar {
    height: 4px;
  }

  .suz-company-scroll::-webkit-scrollbar-track {
    background: transparent;
  }

  .suz-company-scroll::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 2px;
  }
}

/* Enhanced Company Showcase Responsive Design */
@media (max-width: 768px) {
  .suz-company-showcase {
    padding: var(--space-16) 0 !important;
    margin-top: var(--space-12) !important;
    margin-bottom: var(--space-12) !important;
    /* Ensure proper overflow handling on mobile */
    overflow-x: hidden;
  }

  .suz-company-card {
    min-width: 260px;
    max-width: 300px;
    /* Ensure cards maintain proper spacing on mobile */
    flex-shrink: 0;
  }

  .animate-scroll-right {
    animation-duration: 45s; /* Faster on mobile for better UX */
    /* Force animation to work on mobile */
    animation-play-state: running !important;
    /* Ensure proper transform origin */
    transform-origin: left center;
  }

  .suz-company-scroll {
    /* Ensure proper width calculation on mobile */
    width: max-content;
    /* CRITICAL: Don't override animation transform - use will-change instead */
    will-change: transform;
    /* Allow touch events but prevent scrolling interference */
    touch-action: pan-y pinch-zoom;
    /* Force hardware acceleration without overriding animation */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  /* Adjust fade gradients for mobile */
  .suz-company-showcase .absolute.w-32 {
    width: 4rem; /* Smaller fade areas on mobile */
  }

  /* Improve touch interaction on mobile */
  .suz-company-showcase .suz-card-glass:hover {
    transform: none !important; /* Disable hover effects on mobile */
    background: rgba(28, 28, 30, 0.9) !important;
    box-shadow:
      0 20px 40px -10px rgba(0, 0, 0, 0.6),
      0 10px 25px -5px rgba(10, 132, 255, 0.1),
      0 2px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .suz-company-showcase {
    overflow-x: hidden !important;
  }

  .suz-company-card {
    min-width: 240px;
    max-width: 280px;
  }

  .animate-scroll-right {
    animation-duration: 40s;
    /* Ensure animation works on small screens */
    animation-play-state: running !important;
    /* Don't override animation transform */
    will-change: transform;
  }

  .suz-company-scroll {
    /* Force proper width on very small screens */
    width: max-content !important;
    /* Don't override animation transform - use translateZ for hardware acceleration */
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
  }
}

/* Tablet and medium screens */
@media (min-width: 769px) and (max-width: 1024px) {
  .suz-company-card {
    min-width: 280px;
    max-width: 320px;
  }

  .animate-scroll-right {
    animation-duration: 50s; /* Medium speed for tablets */
    /* Ensure consistent animation on tablets */
    animation-play-state: running;
  }

  .suz-company-scroll {
    transform: translate3d(0, 0, 0);
  }
}

/* Large screens optimization */
@media (min-width: 1025px) {
  .animate-scroll-right {
    animation-duration: 60s; /* Original speed for desktop */
  }
}

/* Fallback for browsers with poor animation support */
@supports not (animation: scroll-right 60s linear infinite) {
  .suz-company-scroll {
    overflow-x: auto;
    scroll-behavior: smooth;
  }

  .animate-scroll-right {
    animation: none;
  }
}

/* Force animation on supported browsers */
@supports (animation: scroll-right 60s linear infinite) {
  .animate-scroll-right {
    animation-play-state: running;
  }
}

/* Enhanced premium glass morphism for company cards */
.suz-company-showcase .suz-card-glass {
  background: rgba(28, 28, 30, 0.9) !important;
  -webkit-backdrop-filter: blur(25px) !important;
  backdrop-filter: blur(25px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow:
    0 20px 40px -10px rgba(0, 0, 0, 0.6),
    0 10px 25px -5px rgba(10, 132, 255, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.suz-company-showcase .suz-card-glass:hover {
  background: rgba(44, 44, 46, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow:
    0 25px 50px -10px rgba(0, 0, 0, 0.7),
    0 15px 30px -5px rgba(10, 132, 255, 0.2),
    0 5px 15px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-5px) scale(1.02) !important;
}

.suz-company-showcase .suz-card-glass:hover {
  background: rgba(58, 58, 60, 0.9) !important;
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.6),
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(10, 132, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

/* Enhanced Button Interactions */
.premium-button-enhanced {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-button-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.premium-button-enhanced:hover::before {
  left: 100%;
}

.premium-button-enhanced:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.4);
}

/* Smooth Card Hover Effects */
.card-hover-enhanced {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.card-hover-enhanced:hover {
  transform: translateY(-8px) rotateX(2deg);
  box-shadow:
    0 25px 50px -10px rgba(0, 0, 0, 0.15),
    0 10px 30px -5px rgba(59, 130, 246, 0.1);
}

/* Text Reveal Animation */
@keyframes text-reveal {
  from {
    opacity: 0;
    transform: translateY(20px);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

.text-reveal {
  animation: text-reveal 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Smooth Scrolling */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Accessibility: Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Keep essential focus transitions */
  *:focus {
    transition-duration: 0.1s !important;
  }
}

/* Enhanced Custom Scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(6, 182, 212, 0.4));
  border-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(6, 182, 212, 0.6));
}

/* Mobile Responsiveness Enhancements */
@media (max-width: 768px) {
  .glass-morphism-premium {
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
  }

  .logo-glow img {
    min-width: 40px !important;
    min-height: 40px !important;
  }

  .service-card-premium {
    margin-bottom: 1rem;
  }

  .team-card {
    margin-bottom: 1rem;
  }

  /* Enhanced mobile typography */
  .suz-text-display-xl {
    font-size: 2.5rem !important;
    line-height: 1.1 !important;
  }

  .suz-text-display-lg {
    font-size: 2rem !important;
    line-height: 1.2 !important;
  }

  .suz-text-heading-xl {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
  }

  /* Enhanced mobile navigation improvements */
  nav[role="navigation"] {
    top: 1rem !important;
    /* Ensure perfect centering on mobile */
    left: 50% !important;
    transform: translateX(-50%) !important;
    max-width: calc(100vw - 1rem) !important;
  }

  nav .suz-card-glass {
    padding: 0.5rem 0.75rem !important;
    margin: 0 auto !important;
  }

  nav .flex {
    flex-wrap: nowrap !important;
    gap: 0.25rem !important;
    justify-content: center !important;
    align-items: center !important;
  }

  .suz-nav-link {
    font-size: 0.8rem !important;
    padding: 0.4rem 0.6rem !important;
    white-space: nowrap !important;
    text-align: center !important;
  }

  /* Mobile button improvements */
  .suz-btn-primary {
    padding: var(--space-4) var(--space-8) !important;
    font-size: 1rem !important;
    min-height: 48px; /* Touch target size */
  }

  /* Mobile spacing adjustments */
  section {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

/* Tablet responsiveness */
@media (min-width: 769px) and (max-width: 1024px) {
  .suz-text-display-xl {
    font-size: 3.5rem;
  }

  .grid.lg\\:grid-cols-3 {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .grid.lg\\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* Performance Optimization Classes */
.lazy-load {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.lazy-load.loaded {
  opacity: 1;
}

.image-optimized {
  width: 100%;
  height: auto;
  object-fit: cover;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Preload critical resources */
.critical-resource {
  font-display: swap;
}

/* Apple-Inspired Component Classes - High Priority */

/* Button System */
.suz-btn-primary {
  background: var(--suz-blue-primary) !important;
  color: white !important;
  padding: var(--space-3) var(--space-6) !important;
  border-radius: var(--radius-lg) !important;
  font-weight: var(--font-weight-medium) !important;
  font-size: var(--text-body-lg) !important;
  box-shadow: var(--shadow-md) !important;
  transition: all var(--duration-normal) var(--ease-out-cubic) !important;
  border: none !important;
  cursor: pointer !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-decoration: none !important;
}

.suz-btn-primary:hover {
  background: #0056CC !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.suz-btn-secondary {
  background: var(--suz-surface-primary);
  color: var(--suz-blue-primary);
  border: 1px solid var(--suz-gray-200);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  font-size: var(--text-body-lg);
  transition: all var(--duration-normal) var(--ease-out-cubic);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.suz-btn-secondary:hover {
  background: var(--suz-gray-50);
  border-color: var(--suz-blue-primary);
  transform: translateY(-1px);
}

/* Card System */
.suz-card-primary {
  background: var(--suz-surface-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  padding: var(--space-6);
  border: 1px solid var(--suz-gray-100);
  transition: all var(--duration-normal) var(--ease-out-cubic);
}

.suz-card-primary:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.suz-card-glass {
  background: var(--suz-surface-glass) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: var(--radius-xl) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: var(--shadow-lg) !important;
  padding: var(--space-6) !important;
}

/* Navigation System */
.suz-nav-primary {
  background: rgba(28, 28, 30, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--suz-gray-100);
  padding: var(--space-4) 0;
}

/* Enhanced Navigation Centering */
nav[role="navigation"] {
  /* Ensure perfect horizontal centering */
  position: fixed !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  /* Prevent layout shifts */
  will-change: transform;
  /* Ensure proper stacking */
  z-index: 50;
  /* Ensure content doesn't overflow */
  box-sizing: border-box;
  /* Center the navigation container */
  display: block !important;
  text-align: center;
}

/* Ensure the glass container is centered within the nav */
nav[role="navigation"] > div {
  margin: 0 auto;
  display: inline-block;
}

/* Additional centering support for all screen sizes */
@media (max-width: 480px) {
  nav[role="navigation"] {
    /* Extra small screens - ensure perfect centering */
    left: 50% !important;
    transform: translateX(-50%) !important;
    max-width: calc(100vw - 1rem) !important;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  nav[role="navigation"] {
    /* Small to medium screens */
    left: 50% !important;
    transform: translateX(-50%) !important;
    max-width: calc(100vw - 2rem) !important;
  }
}

@media (min-width: 769px) {
  nav[role="navigation"] {
    /* Large screens and up */
    left: 50% !important;
    transform: translateX(-50%) !important;
    max-width: none !important;
  }
}



.suz-nav-link {
  color: var(--suz-gray-600);
  font-weight: var(--font-weight-medium);
  font-size: var(--text-body-lg);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  transition: all var(--duration-normal) var(--ease-out-cubic);
  text-decoration: none;
  cursor: pointer;
  border: none;
  background: none;
}

.suz-nav-link:hover {
  color: var(--suz-blue-primary);
  background: var(--suz-gray-50);
  transform: scale(1.05);
}

/* Typography Classes */
.suz-text-display-xl {
  font-size: var(--text-display-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-tight) !important;
  letter-spacing: -0.025em !important;
}

.suz-text-display-lg {
  font-size: var(--text-display-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-tight) !important;
  letter-spacing: -0.025em !important;
}

.suz-text-heading-xl {
  font-size: var(--text-heading-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-tight) !important;
  letter-spacing: -0.025em !important;
}

.suz-text-heading-lg {
  font-size: var(--text-heading-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-normal) !important;
}

.suz-text-body-lg {
  font-size: var(--text-body-lg);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
}

/* Enhanced Section Title Class */
.suz-section-title {
  font-size: var(--text-display-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-tight) !important;
  letter-spacing: -0.025em !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* ===== ENHANCED HERO SECTION STYLES ===== */

/* Hero Section Container */
.suz-hero-enhanced {
  position: relative;
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.9) 50%,
    rgba(15, 23, 42, 0.95) 100%);
  overflow: hidden;
}

/* Premium Radial Gradient Background */
.bg-radial-gradient-hero {
  background: radial-gradient(
    ellipse 80% 50% at 50% 40%,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(6, 182, 212, 0.1) 30%,
    rgba(139, 92, 246, 0.08) 60%,
    transparent 100%
  );
}

/* Enhanced Typography Hierarchy */
.suz-hero-headline-container {
  margin-bottom: 2rem;
  animation: hero-fade-in-up 1s ease-out 0.2s both;
}

.suz-hero-title {
  font-size: clamp(2.5rem, 8vw, 5rem) !important;
  font-weight: 800 !important;
  line-height: 1.1 !important;
  letter-spacing: -0.02em !important;
  text-shadow:
    0 4px 8px rgba(0, 0, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2) !important;
  margin: 0 !important;
}

.suz-hero-accent {
  display: inline-block;
  background: linear-gradient(135deg, #3b82f6, #06b6d4, #0ea5e9, #8b5cf6, #3b82f6) !important;
  background-size: 300% 300% !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  animation: gradient-shift 4s ease-in-out infinite, pulse-glow 3s ease-in-out infinite;
}

.suz-hero-main-text {
  color: #f1f5f9 !important;
  font-weight: 700 !important;
}

/* Enhanced Subtitle */
.suz-hero-subtitle-container {
  margin-bottom: 2rem;
  animation: hero-fade-in-up 1s ease-out 0.4s both;
}

.suz-hero-subtitle {
  font-size: clamp(1.25rem, 4vw, 2rem) !important;
  font-weight: 300 !important;
  line-height: 1.4 !important;
  letter-spacing: 0.02em !important;
  color: #cbd5e1 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  margin: 0 !important;
}

/* Enhanced Description */
.suz-hero-description-container {
  margin-bottom: 3rem;
  animation: hero-fade-in-up 1s ease-out 0.6s both;
}

.suz-hero-description {
  font-size: clamp(1rem, 2.5vw, 1.25rem) !important;
  font-weight: 400 !important;
  line-height: 1.6 !important;
  color: #94a3b8 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  margin: 0 !important;
}

/* Hero Animation Keyframes */
@keyframes hero-fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced CTA Button Styles */
.suz-hero-cta-container {
  animation: hero-fade-in-up 1s ease-out 0.8s both;
}

.suz-hero-cta-primary,
.suz-hero-cta-secondary {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2.5rem;
  border-radius: 9999px;
  font-size: 1.125rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  border: none;
  cursor: pointer;
  min-height: 56px;
  min-width: 200px;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.25),
    0 4px 12px -2px rgba(0, 0, 0, 0.1);
}

.suz-hero-cta-primary {
  background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
  color: white;
}

.suz-hero-cta-secondary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.suz-hero-cta-primary:hover,
.suz-hero-cta-secondary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 20px 40px -10px rgba(0, 0, 0, 0.3),
    0 8px 20px -4px rgba(0, 0, 0, 0.15);
}

.suz-hero-cta-primary:hover {
  background: linear-gradient(135deg, #15803d 0%, #166534 100%);
}

.suz-hero-cta-secondary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.suz-hero-cta-content {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
}

.suz-hero-cta-icon {
  width: 1.25rem;
  height: 1.25rem;
  transition: transform 0.3s ease;
}

.suz-hero-cta-text {
  font-weight: 600;
  letter-spacing: 0.025em;
}

.group:hover .suz-hero-cta-content {
  transform: scale(1.05);
}

.group:hover .suz-hero-cta-icon {
  transform: rotate(12deg);
}

/* Premium Button Shine Effect */
.suz-hero-cta-primary::before,
.suz-hero-cta-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.suz-hero-cta-primary:hover::before,
.suz-hero-cta-secondary:hover::before {
  left: 100%;
}

/* Enhanced Floating Elements */
.suz-floating-element-1 {
  animation: float 8s ease-in-out infinite;
}

.suz-floating-element-2 {
  animation: float 8s ease-in-out infinite 2s;
}

.suz-floating-element-3 {
  animation: float 8s ease-in-out infinite 4s;
}

.suz-floating-element-4 {
  animation: float 8s ease-in-out infinite 1s;
}

.suz-floating-element-5 {
  animation: float 8s ease-in-out infinite 3s;
}

/* Enhanced Logo Styles */
.suz-logo-container {
  transition: all 0.3s ease;
}

.suz-logo-wrapper {
  padding: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  background: rgba(28, 28, 30, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.suz-logo-wrapper:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 6px 20px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.suz-logo-enhanced {
  width: 5rem; /* 80px - increased from 64px */
  height: 5rem; /* 80px - increased from 64px */
  min-width: 3rem; /* 48px minimum for mobile */
  min-height: 3rem; /* 48px minimum for mobile */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Responsive Logo Scaling */
@media (max-width: 768px) {
  .suz-logo-enhanced {
    width: 4rem; /* 64px on mobile */
    height: 4rem; /* 64px on mobile */
    min-width: 2.5rem; /* 40px minimum */
    min-height: 2.5rem; /* 40px minimum */
  }

  .suz-logo-wrapper {
    padding: 0.75rem;
  }

  .suz-logo-container {
    top: 1rem !important;
    left: 1rem !important;
  }
}

@media (max-width: 480px) {
  .suz-logo-enhanced {
    width: 3.5rem; /* 56px on small mobile */
    height: 3.5rem; /* 56px on small mobile */
  }

  .suz-logo-wrapper {
    padding: 0.5rem;
  }
}

@media (min-width: 1024px) {
  .suz-logo-enhanced {
    width: 6rem; /* 96px on desktop */
    height: 6rem; /* 96px on desktop */
  }

  .suz-logo-wrapper {
    padding: 1.25rem;
  }
}

/* ===== ENHANCED MOBILE NAVIGATION STYLES ===== */

/* Mobile Menu Button */
.suz-mobile-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0.5rem;
}

.suz-mobile-menu-button:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

/* Hamburger Icon */
.suz-hamburger-icon {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 1.5rem;
  height: 1.125rem;
}

.suz-hamburger-line {
  display: block;
  width: 100%;
  height: 2px;
  background: #f1f5f9;
  border-radius: 1px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

/* Hamburger Animation States */
.suz-hamburger-line-1-open {
  transform: translateY(7px) rotate(45deg);
}

.suz-hamburger-line-2-open {
  opacity: 0;
  transform: scaleX(0);
}

.suz-hamburger-line-3-open {
  transform: translateY(-7px) rotate(-45deg);
}

/* Mobile Menu Overlay */
.suz-mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 40;
  animation: fade-in 0.3s ease-out;
}

.suz-mobile-menu-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

/* Mobile Menu */
.suz-mobile-menu {
  position: fixed;
  top: 5rem;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100vw - 2rem);
  max-width: 20rem;
  z-index: 50;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.suz-mobile-menu-open {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
  pointer-events: auto;
}

.suz-mobile-menu-closed {
  opacity: 0;
  transform: translateX(-50%) translateY(-1rem);
  pointer-events: none;
}

.suz-mobile-menu-content {
  background: rgba(28, 28, 30, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 1rem;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 8px 20px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Mobile Navigation Links */
.suz-mobile-nav-link {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  color: #f1f5f9;
  font-weight: 500;
  font-size: 1rem;
  text-align: center;
  background: none;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.suz-mobile-nav-link:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  transform: translateX(4px);
}

.suz-mobile-nav-link:active {
  transform: translateX(4px) scale(0.98);
}

/* ===== RESPONSIVE HERO ENHANCEMENTS ===== */

/* Mobile Hero Adjustments */
@media (max-width: 768px) {
  .suz-hero-enhanced {
    padding-top: 7rem !important;
  }

  .suz-hero-title {
    font-size: clamp(2rem, 10vw, 3.5rem) !important;
    margin-bottom: 1.5rem !important;
  }

  .suz-hero-subtitle {
    font-size: clamp(1rem, 5vw, 1.5rem) !important;
    margin-bottom: 1.5rem !important;
  }

  .suz-hero-description {
    font-size: clamp(0.875rem, 4vw, 1.125rem) !important;
    margin-bottom: 2rem !important;
    padding: 0 1rem;
  }

  .suz-hero-cta-container {
    padding: 0 1rem;
  }

  .suz-hero-cta-primary,
  .suz-hero-cta-secondary {
    width: 100%;
    max-width: 280px;
    padding: 0.875rem 2rem;
    font-size: 1rem;
    min-height: 52px;
  }

  .suz-hero-cta-icon {
    width: 1.125rem;
    height: 1.125rem;
  }

  /* Adjust floating elements for mobile */
  .suz-floating-element-1,
  .suz-floating-element-2,
  .suz-floating-element-3,
  .suz-floating-element-4,
  .suz-floating-element-5 {
    opacity: 0.6;
    animation-duration: 10s;
  }
}

/* Small Mobile Adjustments */
@media (max-width: 480px) {
  .suz-hero-title {
    font-size: clamp(1.75rem, 12vw, 3rem) !important;
  }

  .suz-hero-subtitle {
    font-size: clamp(0.875rem, 6vw, 1.25rem) !important;
  }

  .suz-hero-description {
    font-size: clamp(0.8rem, 4.5vw, 1rem) !important;
    line-height: 1.5 !important;
  }

  .suz-hero-cta-primary,
  .suz-hero-cta-secondary {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    min-height: 48px;
  }
}

/* Tablet Adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .suz-hero-title {
    font-size: clamp(3rem, 7vw, 4.5rem) !important;
  }

  .suz-hero-subtitle {
    font-size: clamp(1.5rem, 3.5vw, 1.875rem) !important;
  }

  .suz-hero-description {
    font-size: clamp(1.125rem, 2.5vw, 1.25rem) !important;
  }
}

/* ===== ACCESSIBILITY & PERFORMANCE ENHANCEMENTS ===== */

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .suz-hero-enhanced,
  .suz-hero-headline-container,
  .suz-hero-subtitle-container,
  .suz-hero-description-container,
  .suz-hero-cta-container,
  .suz-floating-element-1,
  .suz-floating-element-2,
  .suz-floating-element-3,
  .suz-floating-element-4,
  .suz-floating-element-5,
  .floating-element,
  .gradient-text-animated,
  .pulse-glow,
  .animate-fade-in,
  .hero-fade-in-up {
    animation: none !important;
    transition: none !important;
  }

  .suz-hero-accent {
    background: #3b82f6 !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: unset !important;
    background-clip: unset !important;
    color: #3b82f6 !important;
  }

  .suz-hamburger-line {
    transition: none !important;
  }

  .suz-mobile-menu {
    transition: none !important;
  }

  .suz-logo-enhanced,
  .suz-logo-wrapper,
  .suz-hero-cta-primary,
  .suz-hero-cta-secondary,
  .suz-mobile-nav-link {
    transition: none !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .suz-hero-title,
  .suz-hero-subtitle,
  .suz-hero-description {
    text-shadow: none !important;
  }

  .suz-card-glass,
  .suz-logo-wrapper,
  .suz-mobile-menu-content {
    background: rgba(0, 0, 0, 0.9) !important;
    border: 2px solid #ffffff !important;
  }

  .suz-hero-cta-primary,
  .suz-hero-cta-secondary {
    border: 2px solid #ffffff !important;
  }
}

/* Performance Optimizations */
.suz-hero-enhanced,
.suz-floating-element-1,
.suz-floating-element-2,
.suz-floating-element-3,
.suz-floating-element-4,
.suz-floating-element-5 {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.suz-hero-cta-primary,
.suz-hero-cta-secondary,
.suz-mobile-menu-button,
.suz-logo-wrapper {
  will-change: transform;
  transform: translateZ(0);
}

/* Screen Reader Only Content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus States */
.suz-focus-ring:focus {
  outline: 2px solid var(--suz-blue-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
  font-weight: 300;
  letter-spacing: -0.025em;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

/* Core Web Vitals Optimizations */

/* Optimize Largest Contentful Paint (LCP) */
.optimize-lcp {
  font-display: swap;
  contain: layout style paint;
  will-change: auto;
}

/* Reduce Cumulative Layout Shift (CLS) */
.optimize-cls {
  aspect-ratio: 16/9;
  width: 100%;
  height: auto;
}

/* Improve First Input Delay (FID) */
.optimize-fid {
  touch-action: manipulation;
  -webkit-user-select: none;
  user-select: none;
}

/* Critical above-the-fold content */
.critical-content {
  contain: layout style paint;
  will-change: auto;
}

/* Non-critical content that can be deferred */
.defer-content {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* Optimize animations for performance */
.performance-animation {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize images for Core Web Vitals */
.optimize-image {
  aspect-ratio: attr(width) / attr(height);
  object-fit: cover;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Reduce layout thrashing */
.stable-layout {
  contain: layout;
  min-height: 200px;
}

/* Critical CSS for above-the-fold content */
.hero-critical {
  min-height: 100vh;
  contain: layout style paint;
  will-change: auto;
}

.hero-critical h1 {
  font-display: swap;
  contain: layout style;
}

/* Optimize button interactions */
.btn-optimized {
  touch-action: manipulation;
  will-change: transform;
  backface-visibility: hidden;
}

.btn-optimized:hover,
.btn-optimized:focus {
  transform: translateZ(0) scale(1.05);
}

/* Preload critical fonts */
@font-face {
  font-family: 'Inter';
  font-display: swap;
  font-weight: 100 900;
}

/* Optimize scrolling performance */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Optimize focus states for accessibility and performance */
.focus-optimized:focus {
  outline: 2px solid var(--suz-blue-primary);
  outline-offset: 2px;
  transition: outline-offset 0.1s ease;
}

/* Screen Reader Support */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .glass-morphism,
  .glass-morphism-premium {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #000;
  }

  .gradient-text,
  .gradient-text-animated {
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    color: #000;
  }
}

/* Focus Management */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--suz-blue-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Keyboard Navigation Enhancement */
.keyboard-nav *:focus {
  outline: 3px solid var(--suz-blue-primary);
  outline-offset: 2px;
}

/* Color Blind Friendly Patterns */
.pattern-support {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(0, 0, 0, 0.1) 2px,
    rgba(0, 0, 0, 0.1) 4px
  );
}

/* Dark Theme is now default - removed redundant .dark selectors */



::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.8), rgba(100, 210, 255, 0.8));
}

/* Text Colors - Dark theme is now default */
h1, h2, h3, h4, h5, h6 {
  color: var(--suz-gray-900);
}

p, span, div {
  color: var(--suz-gray-700);
}

/* Focus States - Dark theme is now default */
.suz-focus-ring:focus {
  outline: 2px solid var(--suz-blue-primary);
  outline-offset: 2px;
}

/* End of @layer components */
}

/* Additional utility overrides */
@layer utilities {
  .force-apple-design {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  }
}
